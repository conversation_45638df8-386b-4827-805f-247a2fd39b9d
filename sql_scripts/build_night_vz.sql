CREATE OR REPLACE FUNCTION build_night_vz(
    p_journey subscriber.content_sms.journey%TYPE,
    p_platform subscriber.transaction_sms.platform%TYPE
)
RETURNS TEXT AS
$$
DECLARE
    v_version subscriber.content_sms.version%TYPE;
    v_message subscriber.content_sms.message%TYPE;
    v_phone subscriber.subscriber_sms.customer_phone%TYPE;
    v_email subscriber.subscriber_sms.emailaddress%TYPE;
    v_firstname subscriber.subscriber_sms.firstname%TYPE;
    v_lastname subscriber.subscriber_sms.lastname%TYPE;
    v_shorturl subscriber.subscriber_sms.shorturl%TYPE;
    v_brand_name subscriber.brand.brand_name%TYPE;
    v_vertical subscriber.brand.vertical%TYPE;
    v_count INT := 0;

BEGIN
    FOR v_phone, v_email, v_firstname, v_lastname, v_shorturl, v_brand_name, v_vertical IN
        SELECT s.customer_phone, s.emailaddress, s.firstname, s.lastname, s.shorturl,
               b.brand_name, b.vertical
        FROM subscriber.subscriber_sms s
        JOIN subscriber.brand b ON s.brand_id = b.brand_id
        LEFT JOIN subscriber.optout_sms o ON s.customer_phone = o.phone
        WHERE o.phone IS NULL
          AND s.Blacklisted <> 'true'
          AND s.carrierparent LIKE '%VERIZON%'
          AND s.status <> 'not_mobile'
          AND s.leaddate >= date_trunc('day', now()) - interval '1 day' + interval '17 hours'
          AND s.leaddate <= date_trunc('day', now()) + interval '8 hours'
    LOOP
        SELECT scs.version, scs.message
        INTO v_version, v_message
        FROM subscriber.content_sms scs
        WHERE scs.vertical = v_vertical
          AND scs.journey = p_journey
        ORDER BY RANDOM()
        LIMIT 1;
        
        INSERT INTO subscriber.transaction_sms(phone, email, firstname, lastname, version, message, platform, campaign_date)
        VALUES (
            v_phone,
            v_email,
            v_firstname,
            v_lastname,
            v_version,
            CONCAT(v_firstname, ', ', v_message, ' ', v_shorturl, '?A=', v_version,'&subid4=', to_hex(CAST(EXTRACT(EPOCH FROM CURRENT_DATE) AS INTEGER))),
            p_platform,
            CURRENT_DATE
        );
        
        v_count := v_count + 1;
    END LOOP;

    RETURN CONCAT('Successfully inserted ', v_count, ' row(s) into transaction_sms.');
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN CONCAT('Error: ', SQLERRM);
END;
$$
LANGUAGE plpgsql;