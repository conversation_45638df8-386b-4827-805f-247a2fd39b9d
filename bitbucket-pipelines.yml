image: node:20-alpine

pipelines:
  branches:
    dev:
      - step:
          name: "Build and Push docker image to CR DEV"
          services:
            - docker
          caches:
            - node
            - docker
          script:
            - echo "$(date)"
            - export IMAGE_NAME=${CONTAINER_REGISTRY_DEV}/n8n-control-center-dev
            - export DOCKER_BUILD_VER=${MAJOR_VER}.${BITBUCKET_BUILD_NUMBER} && export DOCKER_RELEASE_VER=${DOCKER_BUILD_VER}-${BITBUCKET_COMMIT::8};
            - echo "Building $IMAGE_NAME, version $DOCKER_BUILD_VER, release $DOCKER_RELEASE_VER"
            - docker login -u $CR_USER_DEV -p $CR_USER_PASS_DEV $CONTAINER_REGISTRY_DEV
            - docker build -t $IMAGE_NAME:$BITBUCKET_BUILD_NUMBER -t $IMAGE_NAME:latest --build-arg DOCKER_BUILD_VER=$DOCKER_BUILD_VER --build-arg DOCKER_RELEASE_VER=$DOCKER_RELEASE_VER --build-arg N8N_VERSION=1.98.1 -f Dockerfile .
            - docker tag $IMAGE_NAME $IMAGE_NAME:latest
            - docker tag $IMAGE_NAME $IMAGE_NAME:$BITBUCKET_BUILD_NUMBER
            - docker push $IMAGE_NAME:$BITBUCKET_BUILD_NUMBER
            - docker push $IMAGE_NAME:latest
      - step:
          name: "Deploy to DataCenter K8s DEV"
          runs-on:
            - datacenter
          trigger: manual
          image: atlassian/pipelines-kubectl
          script:
            # NOTE: $KUBECONFIG is secret stored as a base64 encoded string
            # Base64 decode our kubeconfig file into a temporary kubeconfig.yml file (this will be destroyed automatically after this step runs)
            - echo $KUBECONFIG_DC | base64 -d > kubeconfig.yml
            # Tell our Kubernetes deployment to use the new Docker image tag
            - kubectl --kubeconfig=kubeconfig.yml rollout restart deployment n8n-cc-dev-deployment -n marketing
    prod:
      - step:
          name: "Build and Push docker image to CR Prod"
          services:
            - docker
          caches:
            - node
            - docker
          script:
            - echo "$(date)"
            - export IMAGE_NAME=${CONTAINER_REGISTRY}/n8n-control-center-prd
            - export DOCKER_BUILD_VER=${MAJOR_VER}.${BITBUCKET_BUILD_NUMBER} && export DOCKER_RELEASE_VER=${DOCKER_BUILD_VER}-${BITBUCKET_COMMIT::8};
            - echo "Building $IMAGE_NAME, version $DOCKER_BUILD_VER, release $DOCKER_RELEASE_VER"
            - docker login -u $CR_USER -p $CR_USER_PASS $CONTAINER_REGISTRY
            - docker build -t $IMAGE_NAME:$BITBUCKET_BUILD_NUMBER -t $IMAGE_NAME:latest --build-arg DOCKER_BUILD_VER=$DOCKER_BUILD_VER --build-arg DOCKER_RELEASE_VER=$DOCKER_RELEASE_VER --build-arg N8N_VERSION=1.98.1 -f Dockerfile .
            - docker tag $IMAGE_NAME $IMAGE_NAME:latest
            - docker tag $IMAGE_NAME $IMAGE_NAME:$BITBUCKET_BUILD_NUMBER
            - docker push $IMAGE_NAME:$BITBUCKET_BUILD_NUMBER
            - docker push $IMAGE_NAME:latest
      - step:
          name: "Deploy to DataCenter K8s Prod"
          runs-on:
            - datacenter
          trigger: manual
          image: atlassian/pipelines-kubectl
          script:
            # NOTE: $KUBECONFIG is secret stored as a base64 encoded string
            # Base64 decode our kubeconfig file into a temporary kubeconfig.yml file (this will be destroyed automatically after this step runs)
            - echo $KUBECONFIG_DC | base64 -d > kubeconfig.yml
            # Tell our Kubernetes deployment to use the new Docker image tag
            - kubectl --kubeconfig=kubeconfig.yml rollout restart deployment n8n-cc-prd-deployment -n marketing
